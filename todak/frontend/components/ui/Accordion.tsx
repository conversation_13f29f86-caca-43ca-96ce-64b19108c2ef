// components/ui/AccordionItem.tsx
import React, { useState } from 'react';
import { View, Text, TouchableOpacity, LayoutAnimation, Platform, UIManager } from 'react-native';
import { ChevronDown } from 'lucide-react-native';
import { styled } from 'nativewind/react'; // ✅ Works in v4.x

if (Platform.OS === 'android') {
  UIManager.setLayoutAnimationEnabledExperimental?.(true);
}

const StyledView = styled(View);
const StyledText = styled(Text);
const StyledTouchable = styled(TouchableOpacity);

const Accordion = ({
  title,
  children,
  defaultOpen = false,
}: {
  title: string;
  children: React.ReactNode;
  defaultOpen?: boolean;
}) => {
  const [expanded, setExpanded] = useState(defaultOpen);

  const toggleExpand = () => {
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
    setExpanded(!expanded);
  };

  return (
    <StyledView className="border-b border-border px-4">
      <StyledTouchable
        className="flex-row justify-between items-center py-4"
        onPress={toggleExpand}
        activeOpacity={0.7}
      >
        <StyledText className="font-semibold text-base text-foreground">{title}</StyledText>
        <ChevronDown
          className={`text-muted-foreground transition-transform duration-200 ${
            expanded ? 'rotate-180' : ''
          }`}
          size={20}
        />
      </StyledTouchable>

      {expanded && (
        <StyledView className="pt-2 pb-4 px-1">
          {children}
        </StyledView>
      )}
    </StyledView>
  );
};

export default Accordion;
